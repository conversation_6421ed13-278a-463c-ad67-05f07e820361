// Dummy Data
const courseData = {
    algebra: {
        videos: [
            {
                id: 1,
                title: "مقدمة في الجبر",
                description: "تعلم أساسيات الجبر والمتغيرات",
                thumbnail: "📊",
                youtubeId: "dQw4w9WgXcQ",
                isFree: true
            },
            {
                id: 2,
                title: "حل المعادلات الخطية",
                description: "طرق حل المعادلات من الدرجة الأولى",
                thumbnail: "📈",
                youtubeId: "dQw4w9WgXcQ",
                isFree: false
            },
            {
                id: 3,
                title: "المعادلات التربيعية",
                description: "حل المعادلات من الدرجة الثانية",
                thumbnail: "📉",
                youtubeId: "dQw4w9WgXcQ",
                isFree: true
            }
        ],
        pdfs: [
            {
                id: 1,
                title: "ملخص الجبر الأساسي",
                description: "ملف شامل لأساسيات الجبر",
                size: "2.5 MB",
                isFree: true,
                fileUrl: "#"
            },
            {
                id: 2,
                title: "تمارين الجبر المتقدم",
                description: "مجموعة تمارين متنوعة مع الحلول",
                size: "4.1 MB",
                isFree: false,
                fileUrl: "#"
            }
        ]
    },
    vectors: {
        videos: [
            {
                id: 4,
                title: "مقدمة في الأشعة",
                description: "فهم مفهوم الأشعة في الرياضيات",
                thumbnail: "🔢",
                youtubeId: "dQw4w9WgXcQ",
                isFree: true
            },
            {
                id: 5,
                title: "عمليات الأشعة",
                description: "الجمع والطرح والضرب القياسي",
                thumbnail: "➕",
                youtubeId: "dQw4w9WgXcQ",
                isFree: false
            }
        ],
        pdfs: [
            {
                id: 3,
                title: "دليل الأشعة الشامل",
                description: "كل ما تحتاج معرفته عن الأشعة",
                size: "3.2 MB",
                isFree: true,
                fileUrl: "#"
            }
        ]
    },
    analysis: {
        videos: [
            {
                id: 6,
                title: "مقدمة في التحليل",
                description: "أساسيات التحليل الرياضي",
                thumbnail: "∫",
                youtubeId: "dQw4w9WgXcQ",
                isFree: true
            },
            {
                id: 7,
                title: "التفاضل والتكامل",
                description: "شرح مفصل للتفاضل والتكامل",
                thumbnail: "∂",
                youtubeId: "dQw4w9WgXcQ",
                isFree: false
            }
        ],
        pdfs: [
            {
                id: 4,
                title: "كتاب التحليل الرياضي",
                description: "مرجع شامل في التحليل",
                size: "8.7 MB",
                isFree: false,
                fileUrl: "#"
            }
        ]
    },
    sequences: {
        videos: [
            {
                id: 8,
                title: "المتتاليات الحسابية",
                description: "فهم المتتاليات الحسابية وخصائصها",
                thumbnail: "🔢",
                youtubeId: "dQw4w9WgXcQ",
                isFree: true
            },
            {
                id: 9,
                title: "المتتاليات الهندسية",
                description: "دراسة المتتاليات الهندسية",
                thumbnail: "📊",
                youtubeId: "dQw4w9WgXcQ",
                isFree: false
            }
        ],
        pdfs: [
            {
                id: 5,
                title: "ملخص المتتاليات",
                description: "ملخص شامل لجميع أنواع المتتاليات",
                size: "2.8 MB",
                isFree: true,
                fileUrl: "#"
            }
        ]
    }
};

// Global Variables
let currentSection = 'home';
let currentCategory = '';
let currentTab = 'videos';
let currentFilter = 'all';
let searchTerm = '';

// DOM Elements
const navLinks = document.querySelectorAll('.nav-link');
const categoryCards = document.querySelectorAll('.category-card');
const sections = document.querySelectorAll('section');
const hamburger = document.getElementById('hamburger');
const navMenu = document.getElementById('nav-menu');
const themeToggle = document.getElementById('theme-toggle');
const searchInput = document.getElementById('search-input');
const searchBtn = document.getElementById('search-btn');
const filterBtns = document.querySelectorAll('.filter-btn');
const tabBtns = document.querySelectorAll('.tab-btn');
const videoModal = document.getElementById('video-modal');
const closeModal = document.getElementById('close-modal');
const videoIframe = document.getElementById('video-iframe');
const scrollTopBtn = document.getElementById('scroll-top');
const searchFilterSection = document.getElementById('search-filter');

// Initialize App
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    showSection('home');
    initializeTheme();
    handleScrollTop();
});

// Event Listeners
function initializeEventListeners() {
    // Navigation
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const section = link.getAttribute('data-section');
            showSection(section);
            updateActiveNavLink(link);
            closeNavMenu();
        });
    });

    // Category Cards
    categoryCards.forEach(card => {
        card.addEventListener('click', () => {
            const section = card.getAttribute('data-section');
            showSection(section);
            updateActiveNavLink(document.querySelector(`[data-section="${section}"]`));
        });
    });

    // Hamburger Menu
    hamburger.addEventListener('click', toggleNavMenu);

    // Theme Toggle
    themeToggle.addEventListener('click', toggleTheme);

    // Search
    searchBtn.addEventListener('click', handleSearch);
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') handleSearch();
    });
    searchInput.addEventListener('input', handleSearch);

    // Filter Buttons
    filterBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            currentFilter = btn.getAttribute('data-filter');
            updateActiveFilter(btn);
            if (currentCategory) renderContent(currentCategory, currentTab);
        });
    });

    // Tab Buttons
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('tab-btn')) {
            currentTab = e.target.getAttribute('data-tab');
            updateActiveTab(e.target);
            if (currentCategory) renderContent(currentCategory, currentTab);
        }
    });

    // Modal
    closeModal.addEventListener('click', closeVideoModal);
    videoModal.addEventListener('click', (e) => {
        if (e.target === videoModal) closeVideoModal();
    });

    // Scroll to Top
    scrollTopBtn.addEventListener('click', () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    window.addEventListener('scroll', handleScrollTop);

    // Login and Subscribe buttons
    document.getElementById('login-btn').addEventListener('click', () => {
        alert('تسجيل الدخول - هذه ميزة تجريبية');
    });

    document.getElementById('subscribe-btn').addEventListener('click', () => {
        alert('الاشتراك - هذه ميزة تجريبية');
    });
}

// Navigation Functions
function showSection(sectionName) {
    // Hide all sections
    sections.forEach(section => {
        section.style.display = 'none';
    });

    // Show target section
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
        targetSection.style.display = 'block';
        currentSection = sectionName;
    }

    // Show/hide search filter
    if (['algebra', 'vectors', 'analysis', 'sequences'].includes(sectionName)) {
        searchFilterSection.style.display = 'block';
        currentCategory = sectionName;
        renderContent(sectionName, currentTab);
    } else {
        searchFilterSection.style.display = 'none';
        currentCategory = '';
    }

    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

function updateActiveNavLink(activeLink) {
    navLinks.forEach(link => link.classList.remove('active'));
    if (activeLink) activeLink.classList.add('active');
}

function toggleNavMenu() {
    navMenu.classList.toggle('active');
    hamburger.classList.toggle('active');
}

function closeNavMenu() {
    navMenu.classList.remove('active');
    hamburger.classList.remove('active');
}

// Theme Functions
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    updateThemeIcon(savedTheme);
}

function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    updateThemeIcon(newTheme);
}

function updateThemeIcon(theme) {
    themeToggle.textContent = theme === 'dark' ? '☀️' : '🌙';
}

// Search and Filter Functions
function handleSearch() {
    searchTerm = searchInput.value.toLowerCase().trim();
    if (currentCategory) renderContent(currentCategory, currentTab);
}

function updateActiveFilter(activeBtn) {
    filterBtns.forEach(btn => btn.classList.remove('active'));
    activeBtn.classList.add('active');
}

function updateActiveTab(activeBtn) {
    const tabBtns = activeBtn.parentElement.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => btn.classList.remove('active'));
    activeBtn.classList.add('active');
}

// Content Rendering
function renderContent(category, type) {
    const contentGrid = document.getElementById(`${category}-content`);
    const data = courseData[category][type];
    
    if (!data || !contentGrid) return;

    // Filter data
    let filteredData = data.filter(item => {
        const matchesSearch = !searchTerm || 
            item.title.toLowerCase().includes(searchTerm) ||
            item.description.toLowerCase().includes(searchTerm);
        
        const matchesFilter = currentFilter === 'all' ||
            (currentFilter === 'free' && item.isFree) ||
            (currentFilter === 'paid' && !item.isFree);
        
        return matchesSearch && matchesFilter;
    });

    // Render cards
    contentGrid.innerHTML = filteredData.map(item => {
        if (type === 'videos') {
            return createVideoCard(item);
        } else {
            return createPdfCard(item);
        }
    }).join('');

    // Add event listeners to new cards
    addCardEventListeners();
}

function createVideoCard(video) {
    const badgeClass = video.isFree ? 'badge-free' : 'badge-premium';
    const badgeText = video.isFree ? 'مجاني' : 'للمشتركين';
    const lockOverlay = !video.isFree ? `
        <div class="lock-overlay">
            <div class="lock-icon">🔒</div>
            <button class="btn btn-primary">اشترك للوصول</button>
        </div>
    ` : '';

    return `
        <div class="content-card video-card" data-video-id="${video.id}">
            <div class="card-thumbnail">
                ${video.thumbnail}
                ${lockOverlay}
            </div>
            <div class="card-content">
                <h3 class="card-title">${video.title}</h3>
                <p class="card-description">${video.description}</p>
                <div class="card-meta">
                    <span class="card-badge ${badgeClass}">${badgeText}</span>
                </div>
                <div class="card-actions">
                    <button class="card-btn btn-play ${!video.isFree ? 'btn-locked' : ''}" 
                            ${video.isFree ? `onclick="playVideo('${video.youtubeId}')"` : 'disabled'}>
                        ${video.isFree ? 'تشغيل' : 'مقفل'}
                    </button>
                </div>
            </div>
        </div>
    `;
}

function createPdfCard(pdf) {
    const badgeClass = pdf.isFree ? 'badge-free' : 'badge-paid';
    const badgeText = pdf.isFree ? 'مجاني' : 'عند الدفع';

    return `
        <div class="content-card pdf-card" data-pdf-id="${pdf.id}">
            <div class="card-thumbnail">
                📄
            </div>
            <div class="card-content">
                <h3 class="card-title">${pdf.title}</h3>
                <p class="card-description">${pdf.description}</p>
                <div class="card-meta">
                    <span class="card-badge ${badgeClass}">${badgeText}</span>
                    <span class="file-size">${pdf.size}</span>
                </div>
                <div class="card-actions">
                    <button class="card-btn ${pdf.isFree ? 'btn-download' : 'btn-locked'}" 
                            ${pdf.isFree ? `onclick="downloadPdf('${pdf.fileUrl}')"` : 'disabled'}>
                        ${pdf.isFree ? 'تنزيل' : 'ادفع للوصول'}
                    </button>
                </div>
            </div>
        </div>
    `;
}

function addCardEventListeners() {
    // Add any additional event listeners for cards if needed
}

// Video Modal Functions
function playVideo(youtubeId) {
    const embedUrl = `https://www.youtube.com/embed/${youtubeId}?autoplay=1`;
    videoIframe.src = embedUrl;
    videoModal.style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeVideoModal() {
    videoModal.style.display = 'none';
    videoIframe.src = '';
    document.body.style.overflow = 'auto';
}

// Download Function
function downloadPdf(fileUrl) {
    if (fileUrl === '#') {
        alert('هذا ملف تجريبي - سيتم تنزيل الملف الفعلي قريباً');
    } else {
        window.open(fileUrl, '_blank');
    }
}

// Scroll to Top Function
function handleScrollTop() {
    if (window.pageYOffset > 300) {
        scrollTopBtn.style.display = 'block';
    } else {
        scrollTopBtn.style.display = 'none';
    }
}

// Keyboard Navigation
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && videoModal.style.display === 'block') {
        closeVideoModal();
    }
});

// Smooth Scrolling for Anchor Links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});
