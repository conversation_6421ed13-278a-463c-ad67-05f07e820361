/* CSS Variables for Theme */
:root {
    --primary-color: #1E3A8A;
    --secondary-color: #1E40AF;
    --accent-color: #3B82F6;
    --tertiary-color: #60A5FA;
    --success-color: #10B981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;

    --bg-color: #F3F4F6;
    --surface-color: #FFFFFF;
    --card-bg: #FFFFFF;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --shadow: 0 4px 6px -1px rgba(30, 58, 138, 0.1);
    --shadow-lg: 0 20px 25px -5px rgba(30, 58, 138, 0.1);
    --shadow-hover: 0 10px 15px -3px rgba(30, 58, 138, 0.2);

    --font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --border-radius: 12px;
    --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="dark"] {
    --bg-color: #111827;
    --surface-color: #1f2937;
    --card-bg: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --border-color: #4b5563;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
    --shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.4);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--bg-color);
    color: var(--text-primary);
    line-height: 1.6;
    transition: var(--transition);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background-color: var(--card-bg);
    box-shadow: var(--shadow);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.header.scrolled {
    background-color: rgba(249, 250, 251, 0.95);
    box-shadow: var(--shadow-hover);
    transform: translateY(0);
}

.header.scrolled .logo-img {
    transform: scale(0.9);
}

.header.scrolled .nav-logo h1 {
    font-size: 1.6rem;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    gap: 2rem;
}

.nav-container .nav-logo {
    order: 1;
}

.nav-container .nav-menu {
    order: 2;
    flex: 1;
    justify-content: center;
}

.nav-container .nav-actions {
    order: 3;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-img {
    height: 40px;
    width: auto;
    object-fit: contain;
    transition: var(--transition);
    filter: drop-shadow(0 2px 4px rgba(30, 58, 138, 0.3));
}

.logo-img:hover {
    transform: scale(1.1) rotate(5deg);
    filter: drop-shadow(0 4px 8px rgba(30, 58, 138, 0.5));
}

.nav-logo h1 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: bold;
    margin: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--accent-color), var(--tertiary-color));
    transition: var(--transition);
    transform: translateX(-50%);
}

.nav-link:hover::before,
.nav-link.active::before {
    width: 80%;
}

.nav-link:hover,
.nav-link.active {
    color: var(--accent-color);
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(96, 165, 250, 0.1));
    transform: translateY(-2px);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Button Styles */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: inline-block;
    text-align: center;
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent-color), var(--tertiary-color));
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
    background: linear-gradient(135deg, var(--tertiary-color), var(--accent-color));
}

.btn-outline {
    background-color: transparent;
    color: var(--accent-color);
    border: 2px solid var(--accent-color);
    box-shadow: var(--shadow);
}

.btn-outline:hover {
    background: linear-gradient(135deg, var(--accent-color), var(--tertiary-color));
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.theme-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: var(--transition);
}

.theme-toggle:hover {
    background-color: var(--surface-color);
}

/* Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    margin: 3px 0;
    transition: var(--transition);
}

/* Main Content */
.main {
    margin-top: 80px;
    min-height: calc(100vh - 80px);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--accent-color));
    color: white;
    padding: 4rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(1deg); }
}

.hero-logo {
    margin-bottom: 2rem;
}

.hero-logo-img {
    height: 120px;
    width: auto;
    object-fit: contain;
    filter: brightness(0) invert(1) drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    opacity: 0.95;
    transition: var(--transition);
    animation: pulse 3s ease-in-out infinite;
}

.hero-logo-img:hover {
    transform: scale(1.1);
    filter: brightness(0) invert(1) drop-shadow(0 8px 16px rgba(0, 0, 0, 0.4));
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.hero-title {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: bold;
}

.hero-description {
    font-size: 1.4rem;
    margin-bottom: 3rem;
    opacity: 0.9;
    font-weight: 500;
}

.hero-about {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto 3rem;
    text-align: right;
}

.about-section {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(96, 165, 250, 0.1));
    opacity: 0;
    transition: var(--transition);
    z-index: 0;
}

.about-section:hover {
    transform: translateY(-8px) scale(1.02);
    background-color: rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-hover);
}

.about-section:hover::before {
    opacity: 1;
}

.about-section > * {
    position: relative;
    z-index: 1;
}

.about-section h2 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #fff;
    font-weight: bold;
}

.about-section p {
    line-height: 1.8;
    opacity: 0.9;
    font-size: 1rem;
}

.hero-cta {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.btn-large::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transition: var(--transition);
    transform: translate(-50%, -50%);
}

.btn-large:hover::after {
    width: 300px;
    height: 300px;
}

.btn-large:active {
    transform: translateY(-2px) scale(0.98);
}

/* Search and Filter */
.search-filter {
    background-color: var(--surface-color);
    padding: 2rem 0;
}

.search-bar {
    display: flex;
    max-width: 500px;
    margin: 0 auto 1.5rem;
    gap: 0.5rem;
}

#search-input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--card-bg);
    color: var(--text-primary);
    font-size: 1rem;
}

#search-btn {
    padding: 0.75rem 1rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 2px solid var(--border-color);
    background-color: var(--card-bg);
    color: var(--text-primary);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.filter-btn.active,
.filter-btn:hover {
    background: linear-gradient(135deg, var(--accent-color), var(--tertiary-color));
    color: white;
    border-color: var(--accent-color);
}

/* Category Sections */
.category-section {
    padding: 3rem 0;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: var(--accent-color);
}

.content-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid var(--border-color);
    background-color: var(--card-bg);
    color: var(--text-primary);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.tab-btn.active,
.tab-btn:hover {
    background: linear-gradient(135deg, var(--accent-color), var(--tertiary-color));
    color: white;
    border-color: var(--accent-color);
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* Card Styles */
.content-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
    border: 1px solid transparent;
}

.content-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, var(--accent-color), var(--tertiary-color));
    opacity: 0;
    transition: var(--transition);
    z-index: -1;
}

.content-card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-8px) scale(1.02);
    border-color: var(--accent-color);
}

.content-card:hover::before {
    opacity: 0.1;
}

.content-card:hover .card-thumbnail {
    transform: scale(1.1);
}

.content-card:hover .card-title {
    color: var(--accent-color);
}

.card-thumbnail {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, var(--surface-color), #dbeafe);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--accent-color);
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.card-thumbnail::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(96, 165, 250, 0.1));
    opacity: 0;
    transition: var(--transition);
}

.card-thumbnail:hover::before {
    opacity: 1;
}

.card-content {
    padding: 1.5rem;
}

.card-title {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.card-description {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.card-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.card-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: var(--transition);
}

.card-badge:hover::before {
    left: 100%;
}

.badge-free {
    background: linear-gradient(135deg, var(--success-color), #059669);
    color: white;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.badge-premium {
    background: linear-gradient(135deg, var(--accent-color), var(--tertiary-color));
    color: white;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.badge-paid {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    box-shadow: 0 2px 4px rgba(30, 58, 138, 0.3);
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.card-btn {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
}

.btn-play {
    background: linear-gradient(135deg, var(--success-color), #059669);
    color: white;
}

.btn-download {
    background: linear-gradient(135deg, var(--accent-color), var(--tertiary-color));
    color: white;
}

.btn-locked {
    background-color: var(--text-secondary);
    color: white;
    cursor: not-allowed;
}

/* Lock Overlay */
.lock-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
}

.lock-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
}

.modal-content {
    position: relative;
    margin: 5% auto;
    width: 90%;
    max-width: 800px;
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.close-btn {
    position: absolute;
    top: 10px;
    right: 20px;
    font-size: 2rem;
    color: white;
    cursor: pointer;
    z-index: 2001;
    background-color: rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Footer */
.footer {
    background-color: var(--surface-color);
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--accent-color);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--accent-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
}

/* Scroll to Top Button */
.scroll-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: linear-gradient(135deg, var(--accent-color), var(--tertiary-color));
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
    cursor: pointer;
    display: none;
    transition: var(--transition);
    z-index: 1000;
    box-shadow: var(--shadow);
}

.scroll-top:hover {
    background: linear-gradient(135deg, var(--tertiary-color), var(--accent-color));
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--card-bg);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-logo-img {
        height: 80px;
    }

    .hero-about {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .btn-large {
        width: 100%;
        max-width: 300px;
    }

    .content-grid {
        grid-template-columns: 1fr;
    }

    .search-bar {
        flex-direction: column;
    }

    .filter-buttons {
        flex-wrap: wrap;
    }

    .nav-actions {
        gap: 0.5rem;
    }

    .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 2rem 0;
    }

    .category-section {
        padding: 2rem 0;
    }

    .content-tabs {
        flex-direction: column;
        align-items: center;
    }

    .modal-content {
        margin: 10% auto;
        width: 95%;
    }
}
