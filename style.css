/* CSS Variables for Theme */
:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --accent-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    
    --bg-color: #ffffff;
    --surface-color: #f8fafc;
    --card-bg: #ffffff;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    
    --font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

[data-theme="dark"] {
    --bg-color: #111827;
    --surface-color: #1f2937;
    --card-bg: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --border-color: #4b5563;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--bg-color);
    color: var(--text-primary);
    line-height: 1.6;
    transition: var(--transition);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background-color: var(--card-bg);
    box-shadow: var(--shadow);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-img {
    height: 40px;
    width: auto;
    object-fit: contain;
}

.nav-logo h1 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: bold;
    margin: 0;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background-color: var(--surface-color);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Button Styles */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--secondary-color);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

.theme-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: var(--transition);
}

.theme-toggle:hover {
    background-color: var(--surface-color);
}

/* Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    margin: 3px 0;
    transition: var(--transition);
}

/* Main Content */
.main {
    margin-top: 80px;
    min-height: calc(100vh - 80px);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.hero-logo {
    margin-bottom: 2rem;
}

.hero-logo-img {
    height: 120px;
    width: auto;
    object-fit: contain;
    filter: brightness(0) invert(1);
    opacity: 0.95;
}

.hero-title {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: bold;
}

.hero-description {
    font-size: 1.4rem;
    margin-bottom: 3rem;
    opacity: 0.9;
    font-weight: 500;
}

.hero-about {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto 3rem;
    text-align: right;
}

.about-section {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    padding: 2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.about-section h2 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #fff;
    font-weight: bold;
}

.about-section p {
    line-height: 1.8;
    opacity: 0.9;
    font-size: 1rem;
}

.hero-cta {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Search and Filter */
.search-filter {
    background-color: var(--surface-color);
    padding: 2rem 0;
}

.search-bar {
    display: flex;
    max-width: 500px;
    margin: 0 auto 1.5rem;
    gap: 0.5rem;
}

#search-input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--card-bg);
    color: var(--text-primary);
    font-size: 1rem;
}

#search-btn {
    padding: 0.75rem 1rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
}

.filter-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 2px solid var(--border-color);
    background-color: var(--card-bg);
    color: var(--text-primary);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.filter-btn.active,
.filter-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Category Sections */
.category-section {
    padding: 3rem 0;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: var(--primary-color);
}

.content-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid var(--border-color);
    background-color: var(--card-bg);
    color: var(--text-primary);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.tab-btn.active,
.tab-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* Card Styles */
.content-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
}

.content-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-thumbnail {
    width: 100%;
    height: 200px;
    background-color: var(--surface-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--text-secondary);
    position: relative;
}

.card-content {
    padding: 1.5rem;
}

.card-title {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.card-description {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.card-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.badge-free {
    background-color: var(--success-color);
    color: white;
}

.badge-premium {
    background-color: var(--warning-color);
    color: white;
}

.badge-paid {
    background-color: var(--danger-color);
    color: white;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.card-btn {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
}

.btn-play {
    background-color: var(--success-color);
    color: white;
}

.btn-download {
    background-color: var(--primary-color);
    color: white;
}

.btn-locked {
    background-color: var(--text-secondary);
    color: white;
    cursor: not-allowed;
}

/* Lock Overlay */
.lock-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
}

.lock-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
}

.modal-content {
    position: relative;
    margin: 5% auto;
    width: 90%;
    max-width: 800px;
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.close-btn {
    position: absolute;
    top: 10px;
    right: 20px;
    font-size: 2rem;
    color: white;
    cursor: pointer;
    z-index: 2001;
    background-color: rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Footer */
.footer {
    background-color: var(--surface-color);
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
    color: var(--text-secondary);
}

/* Scroll to Top Button */
.scroll-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
    cursor: pointer;
    display: none;
    transition: var(--transition);
    z-index: 1000;
}

.scroll-top:hover {
    background-color: var(--secondary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--card-bg);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-logo-img {
        height: 80px;
    }

    .hero-about {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .btn-large {
        width: 100%;
        max-width: 300px;
    }

    .content-grid {
        grid-template-columns: 1fr;
    }

    .search-bar {
        flex-direction: column;
    }

    .filter-buttons {
        flex-wrap: wrap;
    }

    .nav-actions {
        gap: 0.5rem;
    }

    .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 2rem 0;
    }

    .category-section {
        padding: 2rem 0;
    }

    .content-tabs {
        flex-direction: column;
        align-items: center;
    }

    .modal-content {
        margin: 10% auto;
        width: 95%;
    }
}
